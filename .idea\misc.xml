<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ExternalStorageConfigurationManager" enabled="true" />
  <component name="MavenProjectsManager">
    <option name="originalFiles">
      <list>
        <option value="$PROJECT_DIR$/pom.xml" />
      </list>
    </option>
  </component>
  <component name="ProjectInspectionProfilesVisibleTreeState">
    <entry key="Project Default">
      <profile-state>
        <selected-state>
          <State>
            <id>User defined</id>
          </State>
        </selected-state>
      </profile-state>
    </entry>
  </component>
  <component name="ProjectRootManager" version="2" languageLevel="JDK_11" default="true" project-jdk-name="11" project-jdk-type="JavaSDK" />
  <component name="ScriptDefinitionSettings">
    <option name="settings">
      <list>
        <ScriptDefinitionSetting>
          <option name="definitionId" value="org.jetbrains.kotlin.mainKts.MainKtsScript" />
        </ScriptDefinitionSetting>
        <ScriptDefinitionSetting>
          <option name="definitionId" value="kotlin.Any" />
        </ScriptDefinitionSetting>
        <ScriptDefinitionSetting>
          <option name="definitionId" value="kotlin.script.templates.standard.ScriptTemplateWithBindings" />
        </ScriptDefinitionSetting>
        <ScriptDefinitionSetting>
          <option name="definitionId" value="ideBundledScriptDefinition" />
        </ScriptDefinitionSetting>
      </list>
    </option>
  </component>
</project>