<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="Encoding" native2AsciiForPropertiesFiles="true" defaultCharsetForPropertiesFiles="UTF-8">
    <file url="file://$PROJECT_DIR$/cart-service/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/hm-api/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/hm-common/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/hm-gateway/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/hm-service/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/item-service/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/pay-service/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/trade-service/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/user-service/src/main/java" charset="UTF-8" />
  </component>
</project>