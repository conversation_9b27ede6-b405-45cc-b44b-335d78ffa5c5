spring:
  application:
    name: pay-service # 服务名称
  profiles:
    active: dev
  cloud:
    nacos:
      server-addr: 192.168.12.128 # nacos地址
      config:
        file-extension: yml # 文件后缀名
        shared-configs: # 共享配置
          - dataId: shared-jdbc.yml # 共享mybatis配置
          - dataId: shared-log.yml # 共享日志配置
          - dataId: shared-swagger.yml # 共享日志配置
          - dataId: shared-seata.yml # 共享seata配置