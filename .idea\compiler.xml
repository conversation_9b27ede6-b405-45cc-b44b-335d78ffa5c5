<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="CompilerConfiguration">
    <annotationProcessing>
      <profile default="true" name="Default" enabled="true" />
      <profile name="Maven default annotation processors profile" enabled="true">
        <sourceOutputDir name="target/generated-sources/annotations" />
        <sourceTestOutputDir name="target/generated-test-sources/test-annotations" />
        <outputRelativeToContentRoot value="true" />
        <module name="cart-service" />
        <module name="pay-service" />
        <module name="hm-gateway" />
        <module name="trade-service" />
        <module name="hm-common" />
        <module name="item-service" />
        <module name="user-service" />
        <module name="hm-service" />
        <module name="hm-api" />
      </profile>
    </annotationProcessing>
  </component>
  <component name="JavacSettings">
    <option name="ADDITIONAL_OPTIONS_OVERRIDE">
      <module name="cart-service" options="-parameters" />
      <module name="hm-api" options="-parameters" />
      <module name="hm-common" options="-parameters" />
      <module name="hm-gateway" options="-parameters" />
      <module name="hm-service" options="-parameters" />
      <module name="item-service" options="-parameters" />
      <module name="pay-service" options="-parameters" />
      <module name="trade-service" options="-parameters" />
      <module name="user-service" options="-parameters" />
    </option>
  </component>
</project>