spring:
  application:
    name: cart-service
  cloud:
    nacos:
      server-addr: 192.168.12.128:8848
      config:
        file-extension: yml # 配置文件后缀名
        shared-configs: # 共享配置
          - dataId: shared-jdbc.yml # 共享数据库配置
          - dataId: shared-log.yml # 共享日志配置
          - dataId: shared-swagger.yml # 共享Swagger配置
          - dataId: shared-seata.yml # 共享Swagger配置
  profiles:
    active: dev