package com.hmall.api.config;


import com.hmall.api.fallback.CartClientFallback;
import com.hmall.api.fallback.ItemClientFallback;
import com.hmall.api.fallback.TradeClientFallback;
import com.hmall.api.fallback.UserClientFallback;
import com.hmall.common.utils.UserContext;
import feign.Logger;
import feign.RequestInterceptor;
import feign.RequestTemplate;
import org.springframework.context.annotation.Bean;


public class DefaultFeignConfig {

    //注册 CartClientFallback
    @Bean
    public CartClientFallback cartClientFallback(){
        return new CartClientFallback();
    }
    // 注册 TradeClientFallback
    @Bean
    public TradeClientFallback tradeClientFallback(){
        return new TradeClientFallback();
    }
    //注册 UserClientFallback
    @Bean
    public UserClientFallback userClientFallback(){
        return new UserClientFallback();
    }
    @Bean
    public ItemClientFallback itemClientFallback(){
        return new ItemClientFallback();
    }

    @Bean
    public Logger.Level feiLoggerLevel(){
        return Logger.Level.FULL;
    }

    //fegin请求拦截器
    @Bean
    public RequestInterceptor feginRequestInterceptor(){
        return new RequestInterceptor() {
            @Override
            public void apply(RequestTemplate requestTemplate) {
                //获取线程变量中的用户id
                Long userId= UserContext.getUser();
                //设置到feign请求头
                if (userId!=null){
                    requestTemplate.header("user-info",userId.toString());
                }
            }
        };
    }
}
