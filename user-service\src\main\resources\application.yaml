server:
  port: 8083
feign:
  okhttp:
    enabled: true # 开启OKHttp连接池支持
  sentinel:
    enabled: true # 开启Feign对Sentinel的整合
hm:
  swagger:
    title: 用户服务接口文档
    package: com.hmall.user.controller
  db:
    database: hm-user
  jwt:
    location: classpath:hmall.jks
    alias: hmall
    password: hmall123
    tokenTTL: 30m
spring:
  cloud:
    sentinel:
      transport:
        dashboard: http://localhost:8090
      http-method-specify: true # 开启请求方式前缀
